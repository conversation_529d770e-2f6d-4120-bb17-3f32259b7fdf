import cv2
import numpy as np
import time
import threading
import pygame
from datetime import datetime
import json
import os

class FireDetector:
    def __init__(self, config_file='fire_config.json'):
        """
        火焰检测器初始化
        """
        self.config_file = config_file
        self.load_config()
        
        # 火焰检测参数
        self.min_fire_area = self.config.get('min_fire_area', 5000)  # 最小火焰面积阈值
        self.alarm_threshold = self.config.get('alarm_threshold', 0.3)  # 报警阈值（相对于正常火焰面积）
        
        # 状态变量
        self.is_monitoring = False
        self.alarm_active = False
        self.normal_fire_area = None
        self.current_fire_area = 0
        
        # 历史数据
        self.fire_area_history = []
        self.max_history_length = 50
        
        # 报警系统
        self.init_alarm_system()
        
        # 日志
        self.log_file = 'fire_detection_log.txt'
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        else:
            self.config = {
                'min_fire_area': 5000,
                'alarm_threshold': 0.3,
                'hsv_lower': [0, 50, 50],
                'hsv_upper': [30, 255, 255],
                'hsv_lower2': [160, 50, 50],
                'hsv_upper2': [180, 255, 255]
            }
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def init_alarm_system(self):
        """初始化报警系统"""
        try:
            pygame.mixer.init()
            # 创建报警音频（简单的蜂鸣声）
            self.create_alarm_sound()
        except:
            print("警告：无法初始化音频系统")
    
    def create_alarm_sound(self):
        """创建报警声音"""
        duration = 1000  # 毫秒
        sample_rate = 22050
        frames = int(duration * sample_rate / 1000)
        
        arr = np.zeros(frames)
        for i in range(frames):
            # 创建440Hz的正弦波（A音）
            arr[i] = np.sin(2 * np.pi * 440 * i / sample_rate)
        
        # 转换为16位整数
        arr = (arr * 32767).astype(np.int16)
        
        # 创建立体声
        stereo_arr = np.zeros((frames, 2), dtype=np.int16)
        stereo_arr[:, 0] = arr
        stereo_arr[:, 1] = arr
        
        self.alarm_sound = pygame.sndarray.make_sound(stereo_arr)
    
    def detect_fire_area(self, frame):
        """
        检测火焰区域面积
        """
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 定义火焰颜色范围（橙红色）
        lower_red1 = np.array(self.config['hsv_lower'])
        upper_red1 = np.array(self.config['hsv_upper'])
        lower_red2 = np.array(self.config['hsv_lower2'])
        upper_red2 = np.array(self.config['hsv_upper2'])
        
        # 创建掩码
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = cv2.bitwise_or(mask1, mask2)
        
        # 形态学操作去除噪声
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        total_fire_area = 0
        fire_contours = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # 过滤小的噪声区域
                total_fire_area += area
                fire_contours.append(contour)
        
        return total_fire_area, fire_contours, mask
    
    def calibrate_normal_fire_area(self, frame):
        """
        校准正常火焰面积
        """
        fire_area, _, _ = self.detect_fire_area(frame)
        if fire_area > self.min_fire_area:
            self.normal_fire_area = fire_area
            self.config['normal_fire_area'] = fire_area
            self.save_config()
            self.log_event(f"校准正常火焰面积: {fire_area}")
            return True
        return False
    
    def check_fire_status(self, current_area):
        """
        检查火焰状态
        """
        if self.normal_fire_area is None:
            return "未校准"
        
        # 更新历史数据
        self.fire_area_history.append(current_area)
        if len(self.fire_area_history) > self.max_history_length:
            self.fire_area_history.pop(0)
        
        # 计算相对面积
        relative_area = current_area / self.normal_fire_area if self.normal_fire_area > 0 else 0
        
        # 判断火焰状态
        if current_area < self.min_fire_area:
            return "火焰熄灭"
        elif relative_area < self.alarm_threshold:
            return "火焰即将熄灭"
        elif relative_area < 0.7:
            return "火焰减弱"
        else:
            return "火焰正常"
    
    def trigger_alarm(self, status):
        """
        触发报警
        """
        if status in ["火焰熄灭", "火焰即将熄灭"] and not self.alarm_active:
            self.alarm_active = True
            self.log_event(f"报警触发: {status}")
            
            # 播放报警声音
            try:
                self.alarm_sound.play(-1)  # 循环播放
            except:
                pass
            
            print(f"🚨 报警: {status}")
        elif status == "火焰正常" and self.alarm_active:
            self.alarm_active = False
            self.log_event("报警解除: 火焰恢复正常")
            
            # 停止报警声音
            try:
                pygame.mixer.stop()
            except:
                pass
            
            print("✅ 报警解除: 火焰恢复正常")
    
    def log_event(self, message):
        """
        记录事件日志
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message)
        
        print(log_message.strip())
    
    def draw_detection_info(self, frame, fire_area, fire_contours, status):
        """
        在图像上绘制检测信息
        """
        # 绘制火焰轮廓
        cv2.drawContours(frame, fire_contours, -1, (0, 255, 0), 2)
        
        # 绘制信息文本
        info_text = [
            f"火焰面积: {fire_area:.0f}",
            f"状态: {status}",
            f"正常面积: {self.normal_fire_area:.0f}" if self.normal_fire_area else "正常面积: 未校准"
        ]
        
        if self.normal_fire_area:
            relative_area = fire_area / self.normal_fire_area
            info_text.append(f"相对面积: {relative_area:.2f}")
        
        # 设置文本颜色
        color = (0, 0, 255) if status in ["火焰熄灭", "火焰即将熄灭"] else (0, 255, 0)
        
        for i, text in enumerate(info_text):
            cv2.putText(frame, text, (10, 30 + i * 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # 绘制报警状态
        if self.alarm_active:
            cv2.putText(frame, "🚨 ALARM 🚨", (frame.shape[1]//2 - 100, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
        
        return frame

    def monitor_camera(self, camera_index=0):
        """
        监控摄像头
        """
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            print("错误：无法打开摄像头")
            return

        self.is_monitoring = True
        self.log_event("开始火焰监控")

        print("火焰监控系统启动")
        print("按键说明：")
        print("  'c' - 校准正常火焰面积")
        print("  's' - 停止报警")
        print("  'q' - 退出监控")
        print("  'r' - 重置配置")

        while self.is_monitoring:
            ret, frame = cap.read()
            if not ret:
                print("错误：无法读取摄像头画面")
                break

            # 检测火焰面积
            fire_area, fire_contours, mask = self.detect_fire_area(frame)
            self.current_fire_area = fire_area

            # 检查火焰状态
            status = self.check_fire_status(fire_area)

            # 触发报警
            self.trigger_alarm(status)

            # 绘制检测信息
            display_frame = self.draw_detection_info(frame, fire_area, fire_contours, status)

            # 显示画面
            cv2.imshow('Fire Detection', display_frame)
            cv2.imshow('Fire Mask', mask)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                if self.calibrate_normal_fire_area(frame):
                    print(f"✅ 校准成功，正常火焰面积: {self.normal_fire_area}")
                else:
                    print("❌ 校准失败，请确保画面中有足够大的火焰")
            elif key == ord('s'):
                if self.alarm_active:
                    self.alarm_active = False
                    pygame.mixer.stop()
                    print("🔇 报警已手动停止")
            elif key == ord('r'):
                self.reset_config()
                print("🔄 配置已重置")

        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        pygame.mixer.quit()
        self.log_event("火焰监控结束")

    def monitor_video_file(self, video_path):
        """
        监控视频文件
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"错误：无法打开视频文件 {video_path}")
            return

        self.is_monitoring = True
        self.log_event(f"开始监控视频文件: {video_path}")

        while self.is_monitoring:
            ret, frame = cap.read()
            if not ret:
                print("视频播放完毕")
                break

            # 检测火焰面积
            fire_area, fire_contours, mask = self.detect_fire_area(frame)
            self.current_fire_area = fire_area

            # 检查火焰状态
            status = self.check_fire_status(fire_area)

            # 触发报警
            self.trigger_alarm(status)

            # 绘制检测信息
            display_frame = self.draw_detection_info(frame, fire_area, fire_contours, status)

            # 显示画面
            cv2.imshow('Fire Detection - Video', display_frame)
            cv2.imshow('Fire Mask - Video', mask)

            # 处理按键
            key = cv2.waitKey(30) & 0xFF  # 控制播放速度
            if key == ord('q'):
                break
            elif key == ord('c'):
                if self.calibrate_normal_fire_area(frame):
                    print(f"✅ 校准成功，正常火焰面积: {self.normal_fire_area}")
                else:
                    print("❌ 校准失败，请确保画面中有足够大的火焰")
            elif key == ord('s'):
                if self.alarm_active:
                    self.alarm_active = False
                    pygame.mixer.stop()
                    print("🔇 报警已手动停止")

        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        pygame.mixer.quit()
        self.log_event("视频监控结束")

    def reset_config(self):
        """
        重置配置
        """
        self.normal_fire_area = None
        self.fire_area_history = []
        self.alarm_active = False
        pygame.mixer.stop()

        # 重置配置文件
        self.config = {
            'min_fire_area': 5000,
            'alarm_threshold': 0.3,
            'hsv_lower': [0, 50, 50],
            'hsv_upper': [30, 255, 255],
            'hsv_lower2': [160, 50, 50],
            'hsv_upper2': [180, 255, 255]
        }
        self.save_config()

    def get_statistics(self):
        """
        获取统计信息
        """
        if not self.fire_area_history:
            return None

        return {
            'current_area': self.current_fire_area,
            'normal_area': self.normal_fire_area,
            'avg_area': np.mean(self.fire_area_history),
            'min_area': np.min(self.fire_area_history),
            'max_area': np.max(self.fire_area_history),
            'alarm_active': self.alarm_active
        }


def main():
    """
    主函数
    """
    detector = FireDetector()

    print("火焰检测系统")
    print("1. 摄像头监控")
    print("2. 视频文件监控")
    print("3. 配置设置")
    print("4. 查看日志")

    choice = input("请选择功能 (1-4): ")

    if choice == '1':
        camera_index = input("请输入摄像头索引 (默认0): ")
        camera_index = int(camera_index) if camera_index.isdigit() else 0
        detector.monitor_camera(camera_index)

    elif choice == '2':
        video_path = input("请输入视频文件路径: ")
        if os.path.exists(video_path):
            detector.monitor_video_file(video_path)
        else:
            print("视频文件不存在")

    elif choice == '3':
        print("配置设置")
        print(f"当前最小火焰面积阈值: {detector.min_fire_area}")
        print(f"当前报警阈值: {detector.alarm_threshold}")

        new_min_area = input("输入新的最小火焰面积阈值 (回车跳过): ")
        if new_min_area.isdigit():
            detector.min_fire_area = int(new_min_area)
            detector.config['min_fire_area'] = int(new_min_area)

        new_alarm_threshold = input("输入新的报警阈值 (0-1, 回车跳过): ")
        try:
            threshold = float(new_alarm_threshold)
            if 0 <= threshold <= 1:
                detector.alarm_threshold = threshold
                detector.config['alarm_threshold'] = threshold
        except:
            pass

        detector.save_config()
        print("配置已保存")

    elif choice == '4':
        if os.path.exists(detector.log_file):
            with open(detector.log_file, 'r', encoding='utf-8') as f:
                print(f.read())
        else:
            print("暂无日志文件")


if __name__ == "__main__":
    main()
