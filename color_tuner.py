import cv2
import numpy as np

def nothing(x):
    pass

def color_tuner(video_path):
    """
    颜色调试工具 - 实时调整HSV参数找到最佳火焰检测范围
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return
    
    # 创建调试窗口
    cv2.namedWindow('Color Tuner')
    cv2.namedWindow('Original')
    cv2.namedWindow('Mask')
    
    # 创建滑动条
    cv2.createTrackbar('H_min', 'Color Tuner', 0, 179, nothing)
    cv2.createTrackbar('S_min', 'Color Tuner', 50, 255, nothing)
    cv2.createTrackbar('V_min', 'Color Tuner', 50, 255, nothing)
    cv2.createTrackbar('H_max', 'Color Tuner', 30, 179, nothing)
    cv2.createTrackbar('S_max', 'Color Tuner', 255, 255, nothing)
    cv2.createTrackbar('V_max', 'Color Tuner', 255, 255, nothing)
    
    # 第二个颜色范围
    cv2.createTrackbar('H2_min', 'Color Tuner', 160, 179, nothing)
    cv2.createTrackbar('S2_min', 'Color Tuner', 50, 255, nothing)
    cv2.createTrackbar('V2_min', 'Color Tuner', 50, 255, nothing)
    cv2.createTrackbar('H2_max', 'Color Tuner', 179, 179, nothing)
    cv2.createTrackbar('S2_max', 'Color Tuner', 255, 255, nothing)
    cv2.createTrackbar('V2_max', 'Color Tuner', 255, 255, nothing)
    
    # 开关
    cv2.createTrackbar('Use_Range2', 'Color Tuner', 1, 1, nothing)
    
    print("颜色调试工具")
    print("调整滑动条找到最佳的火焰检测参数")
    print("按 's' 保存当前参数")
    print("按 'r' 重置参数")
    print("按 'q' 退出")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 循环播放
            continue
        
        # 获取滑动条值
        h_min = cv2.getTrackbarPos('H_min', 'Color Tuner')
        s_min = cv2.getTrackbarPos('S_min', 'Color Tuner')
        v_min = cv2.getTrackbarPos('V_min', 'Color Tuner')
        h_max = cv2.getTrackbarPos('H_max', 'Color Tuner')
        s_max = cv2.getTrackbarPos('S_max', 'Color Tuner')
        v_max = cv2.getTrackbarPos('V_max', 'Color Tuner')
        
        h2_min = cv2.getTrackbarPos('H2_min', 'Color Tuner')
        s2_min = cv2.getTrackbarPos('S2_min', 'Color Tuner')
        v2_min = cv2.getTrackbarPos('V2_min', 'Color Tuner')
        h2_max = cv2.getTrackbarPos('H2_max', 'Color Tuner')
        s2_max = cv2.getTrackbarPos('S2_max', 'Color Tuner')
        v2_max = cv2.getTrackbarPos('V2_max', 'Color Tuner')
        
        use_range2 = cv2.getTrackbarPos('Use_Range2', 'Color Tuner')
        
        # 转换到HSV
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 创建掩码
        lower1 = np.array([h_min, s_min, v_min])
        upper1 = np.array([h_max, s_max, v_max])
        mask1 = cv2.inRange(hsv, lower1, upper1)
        
        if use_range2:
            lower2 = np.array([h2_min, s2_min, v2_min])
            upper2 = np.array([h2_max, s2_max, v2_max])
            mask2 = cv2.inRange(hsv, lower2, upper2)
            mask = cv2.bitwise_or(mask1, mask2)
        else:
            mask = mask1
        
        # 形态学操作
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 计算面积
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        total_area = sum(cv2.contourArea(c) for c in contours if cv2.contourArea(c) > 100)
        
        # 在原图上绘制轮廓和信息
        result = frame.copy()
        cv2.drawContours(result, contours, -1, (0, 255, 0), 2)
        cv2.putText(result, f'Fire Area: {total_area:.0f}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 显示当前参数
        param_text = f'Range1: H({h_min}-{h_max}) S({s_min}-{s_max}) V({v_min}-{v_max})'
        cv2.putText(result, param_text, (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        if use_range2:
            param_text2 = f'Range2: H({h2_min}-{h2_max}) S({s2_min}-{s2_max}) V({v2_min}-{v2_max})'
            cv2.putText(result, param_text2, (10, 80), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示图像
        cv2.imshow('Original', result)
        cv2.imshow('Mask', mask)
        
        key = cv2.waitKey(30) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # 保存参数
            params = {
                'hsv_lower': [h_min, s_min, v_min],
                'hsv_upper': [h_max, s_max, v_max],
                'hsv_lower2': [h2_min, s2_min, v2_min],
                'hsv_upper2': [h2_max, s2_max, v2_max],
                'use_range2': bool(use_range2)
            }
            
            print("\n最佳参数:")
            print(f"第一个颜色范围: H({h_min}-{h_max}) S({s_min}-{s_max}) V({v_min}-{v_max})")
            if use_range2:
                print(f"第二个颜色范围: H({h2_min}-{h2_max}) S({s2_min}-{s2_max}) V({v2_min}-{v2_max})")
            print(f"当前火焰面积: {total_area:.0f}")
            
            # 保存到文件
            with open('color_params.txt', 'w') as f:
                f.write(f"# 火焰检测颜色参数\n")
                f.write(f"lower_red1 = np.array([{h_min}, {s_min}, {v_min}])\n")
                f.write(f"upper_red1 = np.array([{h_max}, {s_max}, {v_max}])\n")
                if use_range2:
                    f.write(f"lower_red2 = np.array([{h2_min}, {s2_min}, {v2_min}])\n")
                    f.write(f"upper_red2 = np.array([{h2_max}, {s2_max}, {v2_max}])\n")
            
            print("参数已保存到 color_params.txt")
            
        elif key == ord('r'):
            # 重置参数
            cv2.setTrackbarPos('H_min', 'Color Tuner', 0)
            cv2.setTrackbarPos('S_min', 'Color Tuner', 50)
            cv2.setTrackbarPos('V_min', 'Color Tuner', 50)
            cv2.setTrackbarPos('H_max', 'Color Tuner', 30)
            cv2.setTrackbarPos('S_max', 'Color Tuner', 255)
            cv2.setTrackbarPos('V_max', 'Color Tuner', 255)
            cv2.setTrackbarPos('H2_min', 'Color Tuner', 160)
            cv2.setTrackbarPos('S2_min', 'Color Tuner', 50)
            cv2.setTrackbarPos('V2_min', 'Color Tuner', 50)
            cv2.setTrackbarPos('H2_max', 'Color Tuner', 179)
            cv2.setTrackbarPos('S2_max', 'Color Tuner', 255)
            cv2.setTrackbarPos('V2_max', 'Color Tuner', 255)
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("使用方法: python color_tuner.py <视频文件路径>")
        print("示例: python color_tuner.py furnace_video.mp4")
    else:
        color_tuner(sys.argv[1])
