import cv2
import numpy as np
import sys

def detect_fire_by_contour(frame):
    """
    基于轮廓检测火焰区域面积
    """
    # 转换为灰度图
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊去噪
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 自适应阈值二值化
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                  cv2.THRESH_BINARY, 11, 2)
    
    # 反转二值图像（让亮区域变成白色）
    binary = cv2.bitwise_not(binary)
    
    # 形态学操作 - 先膨胀后腐蚀（闭运算）
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
    
    # 膨胀 - 连接断开的区域
    dilated = cv2.dilate(binary, kernel, iterations=2)
    
    # 腐蚀 - 去除噪声，恢复原始大小
    eroded = cv2.erode(dilated, kernel, iterations=1)
    
    # 再次膨胀 - 填补内部空洞
    final_mask = cv2.dilate(eroded, kernel, iterations=1)
    
    # 查找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 筛选轮廓
    fire_contours = []
    total_fire_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        # 过滤太小的区域
        if area < 500:
            continue
            
        # 计算轮廓的紧凑度（周长平方/面积）
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
            
        compactness = (perimeter * perimeter) / area
        
        # 火焰通常不是很规则，紧凑度会比较大
        # 过滤掉太规则的形状（如矩形、圆形）
        if compactness < 15:  # 可调参数
            continue
            
        # 计算轮廓的凸包
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        
        # 计算凸性缺陷比例
        if hull_area > 0:
            solidity = area / hull_area
            # 火焰边缘不规则，凸性较低
            if solidity < 0.7:  # 可调参数
                fire_contours.append(contour)
                total_fire_area += area
    
    return total_fire_area, fire_contours, final_mask

def detect_fire_by_brightness(frame):
    """
    基于亮度检测火焰区域（备选方案）
    """
    # 转换为HSV
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # 提取亮度通道
    v_channel = hsv[:, :, 2]
    
    # 高亮度阈值
    _, bright_mask = cv2.threshold(v_channel, 200, 255, cv2.THRESH_BINARY)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    
    # 膨胀
    dilated = cv2.dilate(bright_mask, kernel, iterations=3)
    
    # 腐蚀
    eroded = cv2.erode(dilated, kernel, iterations=2)
    
    # 查找轮廓
    contours, _ = cv2.findContours(eroded, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    fire_contours = []
    total_fire_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 300:  # 过滤小区域
            fire_contours.append(contour)
            total_fire_area += area
    
    return total_fire_area, fire_contours, eroded

def detect_fire_combined(frame):
    """
    结合多种方法检测火焰
    """
    # 方法1：轮廓检测
    area1, contours1, mask1 = detect_fire_by_contour(frame)
    
    # 方法2：亮度检测
    area2, contours2, mask2 = detect_fire_by_brightness(frame)
    
    # 简单的融合策略：取较大的面积
    if area1 > area2:
        return area1, contours1, mask1, "contour"
    else:
        return area2, contours2, mask2, "brightness"

def analyze_video_contour(video_path, alarm_threshold=3000, method="combined"):
    """
    使用轮廓检测分析视频中的火焰
    
    Args:
        video_path: 视频文件路径
        alarm_threshold: 报警阈值
        method: 检测方法 ("contour", "brightness", "combined")
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {video_path}")
        return
    
    print(f"开始分析视频: {video_path}")
    print(f"检测方法: {method}")
    print(f"报警阈值: {alarm_threshold} 像素")
    print("按键说明：")
    print("  'q' - 退出")
    print("  's' - 保存截图")
    print("  '1' - 切换到轮廓检测")
    print("  '2' - 切换到亮度检测")
    print("  '3' - 切换到组合检测")
    print("-" * 50)
    
    frame_count = 0
    alarm_count = 0
    current_method = method
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("视频分析完成")
            break
        
        frame_count += 1
        
        # 根据选择的方法检测火焰
        if current_method == "contour":
            fire_area, fire_contours, mask = detect_fire_by_contour(frame)
            method_name = "轮廓检测"
        elif current_method == "brightness":
            fire_area, fire_contours, mask = detect_fire_by_brightness(frame)
            method_name = "亮度检测"
        else:  # combined
            fire_area, fire_contours, mask, used_method = detect_fire_combined(frame)
            method_name = f"组合检测({used_method})"
        
        # 判断是否需要报警
        if fire_area < alarm_threshold:
            alarm_count += 1
            print(f"🚨 报警! 帧 {frame_count}: 火焰面积 {fire_area:.0f} < 阈值 {alarm_threshold}")
        else:
            print(f"✅ 正常  帧 {frame_count}: 火焰面积 {fire_area:.0f}")
        
        # 在图像上绘制检测结果
        display_frame = frame.copy()
        
        # 绘制火焰轮廓
        cv2.drawContours(display_frame, fire_contours, -1, (0, 255, 0), 2)
        
        # 绘制信息文本
        status = "报警" if fire_area < alarm_threshold else "正常"
        color = (0, 0, 255) if fire_area < alarm_threshold else (0, 255, 0)
        
        cv2.putText(display_frame, f"Method: {method_name}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(display_frame, f"Fire Area: {fire_area:.0f}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(display_frame, f"Status: {status}", (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(display_frame, f"Frame: {frame_count}", (10, 120), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 如果是报警状态，添加醒目的报警标识
        if fire_area < alarm_threshold:
            cv2.putText(display_frame, "ALARM!", (display_frame.shape[1]//2 - 80, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        
        # 显示画面
        cv2.imshow('Fire Detection - Contour', display_frame)
        cv2.imshow('Detection Mask', mask)
        
        # 处理按键
        key = cv2.waitKey(30) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            filename = f"fire_contour_frame_{frame_count}.jpg"
            cv2.imwrite(filename, display_frame)
            print(f"已保存截图: {filename}")
        elif key == ord('1'):
            current_method = "contour"
            print("切换到轮廓检测方法")
        elif key == ord('2'):
            current_method = "brightness"
            print("切换到亮度检测方法")
        elif key == ord('3'):
            current_method = "combined"
            print("切换到组合检测方法")
    
    # 显示统计信息
    print("-" * 50)
    print(f"分析完成!")
    print(f"总帧数: {frame_count}")
    print(f"报警帧数: {alarm_count}")
    print(f"报警率: {alarm_count/frame_count*100:.1f}%")
    
    # 清理资源
    cap.release()
    cv2.destroyAllWindows()

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python fire_contour_detection.py <视频文件路径> [报警阈值] [检测方法]")
        print("检测方法: contour(轮廓) | brightness(亮度) | combined(组合，默认)")
        print("示例: python fire_contour_detection.py video.mp4 3000 combined")
        return
    
    video_path = sys.argv[1]
    
    # 获取报警阈值，默认3000
    alarm_threshold = 3000
    if len(sys.argv) >= 3:
        try:
            alarm_threshold = int(sys.argv[2])
        except ValueError:
            print("警告: 报警阈值必须是整数，使用默认值3000")
    
    # 获取检测方法
    method = "combined"
    if len(sys.argv) >= 4:
        if sys.argv[3] in ["contour", "brightness", "combined"]:
            method = sys.argv[3]
        else:
            print("警告: 检测方法无效，使用默认值combined")
    
    # 检查视频文件是否存在
    import os
    if not os.path.exists(video_path):
        print(f"错误: 视频文件 '{video_path}' 不存在")
        return
    
    # 开始分析
    analyze_video_contour(video_path, alarm_threshold, method)

if __name__ == "__main__":
    main()
