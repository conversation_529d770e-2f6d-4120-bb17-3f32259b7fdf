import cv2
import numpy as np
import sys

def detect_fire_area(frame):
    """
    检测火焰区域面积
    """
    # 转换到HSV颜色空间
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # 定义火焰颜色范围（橙红色和红色）
    # 第一个红色范围 (0-30度)
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([30, 255, 255])
    
    # 第二个红色范围 (160-180度)
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作去除噪声
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    total_fire_area = 0
    fire_contours = []
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 100:  # 过滤小的噪声区域
            total_fire_area += area
            fire_contours.append(contour)
    
    return total_fire_area, fire_contours, mask

def analyze_video(video_path, alarm_threshold=5000):
    """
    分析视频中的火焰，当火焰面积过小时报警
    
    Args:
        video_path: 视频文件路径
        alarm_threshold: 报警阈值，火焰面积小于此值时报警
    """
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {video_path}")
        return
    
    print(f"开始分析视频: {video_path}")
    print(f"报警阈值: {alarm_threshold} 像素")
    print("按 'q' 退出，按 's' 截图保存")
    print("-" * 50)
    
    frame_count = 0
    alarm_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("视频分析完成")
            break
        
        frame_count += 1
        
        # 检测火焰面积
        fire_area, fire_contours, mask = detect_fire_area(frame)
        
        # 判断是否需要报警
        if fire_area < alarm_threshold:
            alarm_count += 1
            print(f"🚨 报警! 帧 {frame_count}: 火焰面积 {fire_area:.0f} < 阈值 {alarm_threshold}")
        else:
            print(f"✅ 正常  帧 {frame_count}: 火焰面积 {fire_area:.0f}")
        
        # 在图像上绘制检测结果
        display_frame = frame.copy()
        
        # 绘制火焰轮廓
        cv2.drawContours(display_frame, fire_contours, -1, (0, 255, 0), 2)
        
        # 绘制信息文本
        status = "报警" if fire_area < alarm_threshold else "正常"
        color = (0, 0, 255) if fire_area < alarm_threshold else (0, 255, 0)
        
        cv2.putText(display_frame, f"Fire Area: {fire_area:.0f}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(display_frame, f"Status: {status}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(display_frame, f"Frame: {frame_count}", (10, 90), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 如果是报警状态，添加醒目的报警标识
        if fire_area < alarm_threshold:
            cv2.putText(display_frame, "ALARM!", (display_frame.shape[1]//2 - 80, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        
        # 显示画面
        cv2.imshow('Fire Detection', display_frame)
        cv2.imshow('Fire Mask', mask)
        
        # 处理按键
        key = cv2.waitKey(30) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # 保存当前帧
            filename = f"fire_frame_{frame_count}.jpg"
            cv2.imwrite(filename, display_frame)
            print(f"已保存截图: {filename}")
    
    # 显示统计信息
    print("-" * 50)
    print(f"分析完成!")
    print(f"总帧数: {frame_count}")
    print(f"报警帧数: {alarm_count}")
    print(f"报警率: {alarm_count/frame_count*100:.1f}%")
    
    # 清理资源
    cap.release()
    cv2.destroyAllWindows()

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python fire_alarm.py <视频文件路径> [报警阈值]")
        print("示例: python fire_alarm.py video.mp4 5000")
        return
    
    video_path = sys.argv[1]
    
    # 获取报警阈值，默认5000
    alarm_threshold = 5000
    if len(sys.argv) >= 3:
        try:
            alarm_threshold = int(sys.argv[2])
        except ValueError:
            print("警告: 报警阈值必须是整数，使用默认值5000")
    
    # 检查视频文件是否存在
    import os
    if not os.path.exists(video_path):
        print(f"错误: 视频文件 '{video_path}' 不存在")
        return
    
    # 开始分析
    analyze_video(video_path, alarm_threshold)

if __name__ == "__main__":
    main()
