# 火焰检测报警系统

这是一个简单的火焰检测系统，用于分析视频中的火焰面积，当火焰面积过小时发出报警。

## 功能特点

- 检测视频中的火焰区域面积
- 当火焰面积小于设定阈值时打印报警信息
- 实时显示检测结果和火焰轮廓
- 支持保存关键帧截图

## 安装依赖

```bash
pip install opencv-python numpy
```

## 使用方法

### 基本用法
```bash
python fire_alarm.py <视频文件路径>
```

### 指定报警阈值
```bash
python fire_alarm.py <视频文件路径> <报警阈值>
```

### 示例
```bash
# 使用默认阈值5000
python fire_alarm.py furnace_video.mp4

# 使用自定义阈值3000
python fire_alarm.py furnace_video.mp4 3000
```

## 操作说明

- **q键**: 退出程序
- **s键**: 保存当前帧截图

## 输出说明

程序会在控制台输出每一帧的检测结果：
- ✅ 正常：火焰面积大于阈值
- 🚨 报警：火焰面积小于阈值

最后会显示统计信息：
- 总帧数
- 报警帧数  
- 报警率

## 参数调整

根据您的具体需求，可以调整以下参数：

1. **报警阈值**: 通过命令行参数设置，建议根据正常火焰大小的30-50%设定
2. **颜色范围**: 如需调整火焰检测的颜色范围，可修改代码中的HSV值
3. **噪声过滤**: 可调整最小轮廓面积来过滤噪声

## 注意事项

- 确保视频文件格式被OpenCV支持（如mp4, avi等）
- 建议先用较小的阈值测试，然后根据实际效果调整
- 光照条件会影响检测效果，建议在稳定光照下使用
